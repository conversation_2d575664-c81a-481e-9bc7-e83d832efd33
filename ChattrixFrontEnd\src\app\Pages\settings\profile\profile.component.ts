import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, Observable, takeUntil } from 'rxjs';

import {
  UserProfileService,
  UserProfile,
} from '../../../Core/Services/UserProfile.service';
import { ThemeService, ThemeMode } from '../../chattrix/Services/Theme.service';
import { ComponentBaseUtil } from '../../../Core/Utils/component-base.util';
import {
  ProfileUpdateService,
  ProfileUpdateRequest,
} from '../Services/profile-update.service';
import { FileUploadService } from '../../authentication/Services/FileUpload.service';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent implements OnInit {
  event: Event | null = null;

  // User profile data
  userProfile$: Observable<UserProfile | null>;
  currentTheme$: Observable<ThemeMode>;

  // Form and UI state
  profileForm: FormGroup;
  isSubmitting = false;

  // Tab navigation
  selectedTabIndex = 0;

  // Profile picture upload
  selectedFile: File | null = null;
  imagePreview: string | null = null;

  // File validation
  maxFileSize = 5 * 1024 * 1024; // 5MB
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userProfileService: UserProfileService,
    private themeService: ThemeService,
    private componentBaseUtil: ComponentBaseUtil,
    private profileUpdateService: ProfileUpdateService,
    private fileUploadService: FileUploadService,
  ) {
    this.userProfile$ = this.userProfileService.userProfile$;
    this.currentTheme$ = this.themeService.currentTheme$;

    // Initialize form
    this.profileForm = this.createProfileForm();
  }

  ngOnInit(): void {
    // Subscribe to user profile changes and populate form
    this.userProfile$.subscribe((profile) => {
      if (profile) {
        this.populateForm(profile);
      }
    });
  }

  /**
   * Creates the reactive form for profile editing
   */
  private createProfileForm(): FormGroup {
    return this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      phoneNumber: [''],
      description: [''],
    });
  }

  /**
   * Populates the form with current user profile data
   */
  private populateForm(profile: UserProfile): void {
    this.profileForm.patchValue({
      fullName: profile.name || '',
      phoneNumber: profile.phoneNumber || '',
      description: profile.description || '',
    });
  }

  /**
   * Gets the display name for the user
   */
  getUserDisplayName(profile: UserProfile | null): string {
    if (!profile) return 'User';
    return profile.displayName || profile.name || profile.email || 'User';
  }

  /**
   * Gets the user initials for avatar display
   */
  getUserInitials(profile: UserProfile | null): string {
    if (!profile) return 'U';
    return profile.initials || 'U';
  }

  /**
   * Gets the role display string
   */
  getRoleDisplay(profile: UserProfile | null): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.role)) {
      return profile.role.join(', ');
    }
    return profile.role || 'User';
  }

  /**
   * Handles tab change navigation
   */
  onTabChange(index: number): void {
    this.selectedTabIndex = index;

    if (index === 1) {
      this.router.navigate(['/settings/change-password']);
    }
  }

  /**
   * Handles profile form submission
   */
  onSubmitProfile(): void {
    if (this.profileForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      // Prepare update data with proper validation
      const formValues = this.profileForm.value;
      const updateData: ProfileUpdateRequest = {};

      // Only include fields that have values
      if (formValues.fullName && formValues.fullName.trim()) {
        updateData.fullName = formValues.fullName.trim();
      }

      if (formValues.phoneNumber && formValues.phoneNumber.trim()) {
        updateData.phoneNumber = formValues.phoneNumber.trim();
      }

      if (formValues.description && formValues.description.trim()) {
        updateData.description = formValues.description.trim();
      }

      if (this.selectedFile) {
        updateData.profileImage = this.selectedFile;
      }

      // Check if there are any updates to make
      const hasUpdates =
        updateData.fullName ||
        updateData.phoneNumber ||
        updateData.description ||
        updateData.profileImage;

      if (!hasUpdates) {
        this.componentBaseUtil.showError(
          'Please make at least one change before updating your profile',
        );
        this.isSubmitting = false;
        return;
      }

      // Create a separate subscription that won't be cancelled by component destruction
      this.profileUpdateService.updateProfile(updateData).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.componentBaseUtil.showSuccess('Profile updated successfully!');

            // Refresh the user profile after update
            this.profileUpdateService.getCurrentProfile().subscribe({
              next: (profileResponse) => {
                if (profileResponse.isSuccess && profileResponse.data) {
                  // Update AuthStateService with new user info
                  this.userProfileService['authState'].setUser(
                    profileResponse.data,
                  );
                }
              },
              error: (err) => {
                // Optionally handle error
              },
            });

            // Clear the selected file and preview after successful upload
            this.selectedFile = null;
            this.imagePreview = null;

            // Reset file input
            const fileInput = document.getElementById(
              'profileFileInput',
            ) as HTMLInputElement;
            if (fileInput) {
              fileInput.value = '';
            }
          } else {
            this.componentBaseUtil.showError(
              response.message || 'Failed to update profile',
            );
          }
          // Always reset isSubmitting after response
          this.isSubmitting = false;
        },
        error: (error) => {
          if (
            this.componentBaseUtil.isComponentActive(
              this.isSubmitting,
              'Profile update',
            )
          ) {
            const errorMessage = this.componentBaseUtil.extractErrorMessage(
              error,
              'An error occurred while updating profile',
            );
            this.componentBaseUtil.showError(errorMessage);
          }
          // Always reset isSubmitting on error
          this.isSubmitting = false;
        },
      });
    } else {
      this.markFormGroupTouched();

      // Show specific validation errors
      if (this.profileForm.get('fullName')?.errors) {
        this.componentBaseUtil.showError('Please enter a valid full name');
      } else if (this.profileForm.get('phoneNumber')?.errors) {
        this.componentBaseUtil.showError('Please enter a valid phone number');
      } else {
        this.componentBaseUtil.showError(
          'Please fix the form errors before submitting',
        );
      }
    }
  }

  /**
   * Handles form cancellation
   */
  onCancelProfile(): void {
    // Reset form to original values
    const currentProfile = this.userProfileService.currentProfile;
    if (currentProfile) {
      this.populateForm(currentProfile);
    }
    this.profileForm.markAsUntouched();
    this.router.navigate(['/settings/profile']);
  }

  /**
   * Marks all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach((key) => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Profile picture upload methods
   */
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file using service
      const validation = this.fileUploadService.validateFile(file);
      if (!validation.isValid) {
        this.componentBaseUtil.showError(validation.error!);
        return;
      }

      this.selectedFile = file;

      // Create image preview using service
      this.fileUploadService
        .createImagePreview(file)
        .then((preview) => {
          this.imagePreview = preview;
        })
        .catch((error) => {
          console.error('Error creating preview:', error);
          this.componentBaseUtil.showError('Failed to create image preview.');
        });
    }
  }

  removeImage(event?: Event): void {
    // Prevent event bubbling to avoid triggering file input
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    this.selectedFile = null;
    this.imagePreview = null;
    // Reset file input
    const fileInput = document.getElementById(
      'profileFileInput',
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  triggerFileInput(event?: Event): void {
    // Prevent default behavior for keyboard events
    if (event) {
      event.preventDefault();
    }

    const fileInput = document.getElementById(
      'profileFileInput',
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  /**
   * Gets form field error message
   */
  getFieldError(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (control.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${control.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }
}
