import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { NotificationService } from '../Services/notification.service';

/**
 * Base utility class for common component functionality
 * Provides reusable methods for error handling, loading states, and notifications
 */
@Injectable({
  providedIn: 'root',
})
export class ComponentBaseUtil {
  constructor(private notificationService: NotificationService) {}

  /**
   * Shows a success notification
   */
  showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  /**
   * Shows an error notification
   */
  showError(message: string): void {
    this.notificationService.showError(message);
  }

  /**
   * Shows an info notification
   */
  showInfo(message: string): void {
    this.notificationService.showInfo(message);
  }

  /**
   * Marks all form controls as touched to trigger validation display
   */
  markFormGroupTouched(formGroup: any): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  createDestroySubject(): Subject<void> {
    return new Subject<void>();
  }

  handleDestroy(destroy$: Subject<void>): void {
    destroy$.next();
    destroy$.complete();
  }

  extractErrorMessage(
    error: any,
    defaultMessage: string = 'An error occurred',
  ): string {
    if (error?.message) {
      return error.message;
    }
    if (error?.error?.message) {
      return error.error.message;
    }
    return defaultMessage;
  }

  handleHttpError(error: any, context: string = 'operation'): string {
    const baseMessage = `An error occurred during ${context}`;

    if (error?.status === 401) {
      return 'You are not authorized to perform this action. Please log in again.';
    }
    if (error?.status === 403) {
      return 'You do not have permission to perform this action.';
    }
    if (error?.status === 404) {
      return 'The requested resource was not found.';
    }
    if (error?.status === 500) {
      return 'Server error occurred. Please try again later.';
    }
    if (error?.status === 0) {
      return 'Network error. Please check your connection and try again.';
    }

    return this.extractErrorMessage(error, baseMessage);
  }

  /**
   * Validates if a component is still active before processing responses
   */
  isComponentActive(
    isSubmitting: boolean,
    componentName: string = 'Component',
  ): boolean {
    if (!isSubmitting) {
      console.warn(`${componentName}: No longer active, ignoring response`);
      return false;
    }
    return true;
  }
}
