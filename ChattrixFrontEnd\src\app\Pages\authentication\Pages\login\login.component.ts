import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import { LoginRequest, AuthState, UserInfo } from '../../Models';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  loginForm: FormGroup;
  hidePassword = true;
  logoLoaded = true; // Assume logo loads successfully by default

  // Authentication state
  isAuthenticated = false;
  isLoading = false;
  user: UserInfo | null = null;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private notificationService: NotificationService,
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;
        this.error = state.error;

        // Redirect if authenticated
        if (state.isAuthenticated && state.user) {
          this.router.navigate(['/dashboard']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onLogin(): void {
    // Clear any previous errors
    this.authState.clearError();

    // Validate form first
    if (!this.loginForm.valid) {
      this.handleFormValidation();
      return;
    }

    const loginData: LoginRequest = this.loginForm.value;

    this.authService.login(loginData).subscribe({
      next: (response) => {
        if (response.data?.requiresOtp) {
          // Handle 2FA requirement
          this.notificationService.showInfo(
            'Please enter the verification code sent to your device.',
          );
          this.router.navigate(['/auth/otp-verification'], {
            queryParams: {
              userId: response.data.userId,
            },
          });
        } else if (response.isSuccess) {
          // Login successful, will be handled by state subscription
          this.notificationService.showSuccess(
            'Login successful! Welcome back.',
          );
          this.router.navigate(['/dashboard']);
        }
      },
    });
  }

  onForgotPassword(): void {
    // Navigate to forgot password page
    this.router.navigate(['/auth/forgot-password']);
  }

  onSignUp(): void {
    // Navigate to sign up page
    this.router.navigate(['/auth/signup']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation - simplified since reactive forms handle validation display
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();
    // Form validation errors are displayed by Angular reactive forms in the template
  }

  // Getter methods for template
  get emailControl() {
    return this.loginForm.get('email');
  }
  get passwordControl() {
    return this.loginForm.get('password');
  }
}
