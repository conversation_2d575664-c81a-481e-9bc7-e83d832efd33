import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NotificationService } from '../../../Core/Services/notification.service';

/**
 * Simplified Authentication Error Handling Service
 * Provides centralized error handling for all authentication operations
 * with user-friendly messages and consistent snackbar display
 */
@Injectable({
  providedIn: 'root',
})
export class AuthNotificationService {
  constructor(private notificationService: NotificationService) {}

  /**
   * Handles authentication errors and displays appropriate user messages
   */
  handleAuthError(error: any): void {
    const message = this.extractErrorMessage(error);
    this.notificationService.showError(message);
  }

  /**
   * Shows success message for authentication operations
   */
  showAuthSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  /**
   * Shows info message for authentication operations
   */
  showAuthInfo(message: string): void {
    this.notificationService.showInfo(message);
  }

  /**
   * Extracts user-friendly error messages from various error formats
   */
  private extractErrorMessage(error: any): string {
    // Handle HTTP error responses
    if (error instanceof HttpErrorResponse) {
      return this.handleHttpError(error);
    }

    // Handle structured auth errors
    if (error?.message) {
      return this.mapErrorMessage(error.message);
    }

    // Handle direct error messages
    if (typeof error === 'string') {
      return this.mapErrorMessage(error);
    }

    // Handle response objects with isSuccess: false
    if (error?.error?.message) {
      return this.mapErrorMessage(error.error.message);
    }

    // Handle backend response format
    if (error?.isSuccess === false && error?.message) {
      return this.mapErrorMessage(error.message);
    }

    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Handles HTTP error responses with appropriate status code mapping
   */
  private handleHttpError(error: HttpErrorResponse): string {
    // Debug log to help troubleshoot HTTP errors (remove in production)
    console.log(
      'AuthNotificationService - HTTP Error:',
      error.status,
      error.error,
    );

    switch (error.status) {
      case 400:
        return this.mapErrorMessage(
          error.error?.message || 'Invalid request. Please check your input.',
        );
      case 401:
        // Use the backend message for 401 errors instead of hardcoded message
        return this.mapErrorMessage(
          error.error?.message ||
            'Invalid email or password. Please try again.',
        );
      case 403:
        return this.mapErrorMessage(
          error.error?.message ||
            'Access denied. Please contact support if this continues.',
        );
      case 404:
        return this.mapErrorMessage(
          error.error?.message || 'Service not found. Please try again later.',
        );
      case 409:
        return this.mapErrorMessage(
          error.error?.message || 'Conflict occurred. Please try again.',
        );
      case 500:
        return 'Server error occurred. Please try again later.';
      case 0:
        return 'Network error. Please check your connection and try again.';
      default:
        return this.mapErrorMessage(
          error.error?.message ||
            'An unexpected error occurred. Please try again.',
        );
    }
  }

  /**
   * Maps backend error messages to user-friendly messages
   */
  private mapErrorMessage(message: string): string {
    if (!message) {
      return 'An unexpected error occurred. Please try again.';
    }

    // Debug log to help troubleshoot error mapping (remove in production)
    console.log('AuthNotificationService - Original error message:', message);

    const lowerMessage = message.toLowerCase();

    // Login errors - check these first before other token-related errors
    if (
      lowerMessage.includes('invalid password') ||
      lowerMessage.includes('password is incorrect') ||
      lowerMessage.includes('wrong password') ||
      lowerMessage.includes('incorrect password')
    ) {
      return 'Invalid email or password. Please check your credentials and try again.';
    }

    if (
      lowerMessage.includes('user not found') ||
      lowerMessage.includes('email not found') ||
      lowerMessage.includes('no user found') ||
      lowerMessage.includes('user does not exist')
    ) {
      return 'No account found with this email address. Please check your email or sign up.';
    }

    // General authentication/credential errors
    if (
      lowerMessage.includes('invalid credentials') ||
      lowerMessage.includes('authentication failed') ||
      lowerMessage.includes('login failed') ||
      lowerMessage.includes('unauthorized')
    ) {
      return 'Invalid email or password. Please check your credentials and try again.';
    }

    if (
      lowerMessage.includes('account is deleted') ||
      lowerMessage.includes('account is inactive')
    ) {
      return 'Your account is inactive. Please contact support to reactivate your account.';
    }

    if (
      lowerMessage.includes('account is locked') ||
      lowerMessage.includes('locked')
    ) {
      return 'Your account has been locked. Please try again later or contact support.';
    }

    // Registration errors
    if (
      lowerMessage.includes('email already exists') ||
      lowerMessage.includes('user with this email already exists')
    ) {
      return 'An account with this email address already exists. Please use a different email or try logging in.';
    }

    if (
      lowerMessage.includes('password') &&
      lowerMessage.includes('requirements')
    ) {
      return 'Password does not meet requirements. Please ensure it has at least 8 characters with uppercase, lowercase, number, and special character.';
    }

    // OTP errors
    if (
      lowerMessage.includes('otp') ||
      lowerMessage.includes('verification code')
    ) {
      if (lowerMessage.includes('expired')) {
        return 'Verification code has expired. Please request a new one.';
      }
      if (
        lowerMessage.includes('invalid') ||
        lowerMessage.includes('incorrect')
      ) {
        return 'Invalid verification code. Please try again.';
      }
      return 'Verification code error. Please try again.';
    }

    // Password reset errors - be more specific to avoid catching other token errors
    if (
      lowerMessage.includes('reset token') ||
      (lowerMessage.includes('reset') && lowerMessage.includes('token')) ||
      lowerMessage.includes('password reset')
    ) {
      if (lowerMessage.includes('expired')) {
        return 'Reset link has expired. Please request a new password reset.';
      }
      if (lowerMessage.includes('invalid')) {
        return 'Invalid reset link. Please request a new password reset.';
      }
    }

    // Network and server errors
    if (
      lowerMessage.includes('network') ||
      lowerMessage.includes('connection')
    ) {
      return 'Network error. Please check your connection and try again.';
    }

    if (lowerMessage.includes('server') || lowerMessage.includes('internal')) {
      return 'Server error occurred. Please try again later.';
    }

    // Validation errors
    if (
      lowerMessage.includes('validation') ||
      lowerMessage.includes('invalid')
    ) {
      return 'Please check your input and try again.';
    }

    // Return the original message if no specific mapping found, but clean it up
    return this.cleanErrorMessage(message);
  }

  /**
   * Cleans up error messages for better user experience
   */
  private cleanErrorMessage(message: string): string {
    // Remove technical details and make more user-friendly
    return message
      .replace(/\b(Error|Exception|Failed)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim()
      .replace(/^[^a-zA-Z]*/, '') // Remove leading non-letters
      .replace(/[.!]*$/, '.') // Ensure proper ending
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
  }
}
