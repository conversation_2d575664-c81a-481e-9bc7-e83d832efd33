﻿
using ChattrixBackend.Core.Entities.UserManagement.ChangePasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.ForgotPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.LoginModel;
using ChattrixBackend.Core.Entities.UserManagement.RegisterModel;
using ChattrixBackend.Core.Entities.UserManagement.ResendOtpRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.ResetPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Core.Entities.UserManagement.ToggleStatusRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.UpdateProfileRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyOtpRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyResetTokenModel;
using ChattrixBackend.Core.Pagination.PagedResponseModel;
using ChattrixBackend.Core.Pagination.PaginationParametersModel;
using ChattrixBackend.Services.AccountServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;


namespace ChattrixBackend.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController : ControllerBase {
        private readonly IAccountService _accountService;

        public AccountController(IAccountService accountService) {
            _accountService = accountService;
        }


        [HttpPost("AddAdmin")]
        [AllowAnonymous]
        public async Task<ActionResult<Response>> Register([FromForm] AddUser register) {

            // Handle file upload
            if (register.ProfileImage != null) {
                var uploadResult = await _accountService.UploadFile(register.ProfileImage, "ProfileImages");
                register.ProfileImageUrl = uploadResult;
            }
            var response = await _accountService.CreateAdminUserAsync(register);
            return Ok(response);
        }



        [HttpPost("Register")]
        [AllowAnonymous]
        public async Task<ActionResult<Response>> AddAdmin([FromForm] AddUser register) {

            // Handle file upload
            if (register.ProfileImage != null) {
                var uploadResult = await _accountService.UploadFile(register.ProfileImage, "ProfileImages");
                register.ProfileImageUrl = uploadResult;
            }
            var response = await _accountService.RegisterUserAsync(register);
            return Ok(response);
        }

        [HttpPost("AddUser")]
        public async Task<ActionResult<Response>> AddUser([FromForm] AddUser register) {


            // Handle file upload
            if (register.ProfileImage != null) {
                var uploadResult = await _accountService.UploadFile(register.ProfileImage, "ProfileImages");
                register.ProfileImageUrl = uploadResult;
            }

            var response = await _accountService.AddUserAsync(register);
            return Ok(response);
        }

        [HttpPut("UpdateUser/{userId}")]

        public async Task<IActionResult> UpdateUser(string userId, [FromBody] UserDetails userDetails) {
            var response = await _accountService.UpdateUserAsync(userId, userDetails);
            return Ok(response);
        }

        [HttpDelete("DeleteUser/{userId}")]

        public async Task<IActionResult> DeleteUser(string userId, [FromBody] ToggleStatusRequest request) {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(currentUserId)) {
                return Unauthorized(new Response {
                    IsSuccess = false,
                    Message = "Unauthorized: Unable to determine the current user"
                });
            }

            var response = await _accountService.DeleteUserAsync(userId, request, currentUserId);
            if (!response.IsSuccess) {
                return BadRequest(response);
            }

            return Ok(response);
        }

        [HttpDelete("DeleteSqlUser")]
        [AllowAnonymous]
        public async Task<IActionResult> DeleteSqlUser(string id) {
            var response = await _accountService.DeleteSQlUser(id);
            if (!response.IsSuccess) {
                return BadRequest(response);
            }
            return Ok(response);
        }

        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<ActionResult<Response>> Login(Login login) {
            var response = await _accountService.LoginAsync(login);
            return Ok(response);
        }

        [HttpPost("VerifyOtp")]
        [AllowAnonymous]
        public async Task<IActionResult> VerifyOtp(VerifyOtpRequest request) {
            var response = await _accountService.VerifyOtpAsync(request);
            return Ok(response);
        }

        [HttpPost("ResendOtp")]
        [AllowAnonymous]
        public async Task<IActionResult> ResendOtp([FromBody] ResendOtpRequest request) {
            var response = await _accountService.ResendOtpAsync(request.UserId);
            return Ok(response);
        }

        [HttpGet("GetUserById/{userId}")]
        public async Task<IActionResult> GetUserById(string userId) {
            var userDetails = await _accountService.GetUserByIdAsync(userId);
            if (userDetails == null) {
                return Ok(new Response {
                    IsSuccess = false,
                    Message = "User not found",
                    Data = null
                });
            }

            return Ok(new Response {
                IsSuccess = true,
                Message = "User retrieved successfully",
                Data = userDetails
            });
        }


        [HttpGet("GetUsers")]
        public async Task<ActionResult<ApiResponse<PagedResponse<UserDetails>>>> GetPagedUsers([FromQuery] PaginationParameters parameters) {
            try {
                var pagedResponse = await _accountService.GetPagedUsersAsync(parameters);
                return Ok(ApiResponse<PagedResponse<UserDetails>>.Success(pagedResponse, "Users retrieved successfully"));
            }
            catch (Exception ex) {
                // Log the exception
                return StatusCode(500, ApiResponse<PagedResponse<UserDetails>>.Failure("An error occurred while retrieving users", ex.Message));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetAll")]
        public async Task<ActionResult<ApiResponse<List<UserDetails>>>> GetAllUsers() {
            try {
                var users = await _accountService.GetAllUsersAsync();
                return Ok(ApiResponse<List<UserDetails>>.Success(users, "All users retrieved successfully"));
            }
            catch (Exception ex) {
                return StatusCode(500, ApiResponse<List<UserDetails>>.Failure("An error occurred while retrieving all users", ex.Message));
            }
        }





        [HttpPost("ChangePassword")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request) {
            if (!ModelState.IsValid) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "Invalid request",
                    Data = ModelState
                });
            }

            var response = await _accountService.ChangePasswordAsync(request);
            if (!response.IsSuccess) {
                return BadRequest(response);
            }

            return Ok(response);
        }

        [HttpPost("ForgotPassword")]
        [AllowAnonymous]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequest request) {
            if (!ModelState.IsValid) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "Invalid request",
                    Data = ModelState
                });
            }

            var response = await _accountService.InitiateForgotPasswordAsync(request);
            return Ok(response);
        }

        [HttpPost("VerifyResetToken")]
        [AllowAnonymous]
        public async Task<IActionResult> VerifyResetToken([FromBody] VerifyResetTokenRequest request) {
            if (!ModelState.IsValid) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "Invalid request",
                    Data = ModelState
                });
            }

            var response = await _accountService.VerifyResetTokenAsync(request);
            return Ok(response);
        }

        [HttpPost("ResetPassword")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request) {
            if (!ModelState.IsValid) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "Invalid request",
                    Data = ModelState
                });
            }

            var response = await _accountService.ResetPasswordAsync(request);
            return Ok(response);
        }

        [HttpPut("UpdateProfile")]
        public async Task<IActionResult> UpdateProfile([FromForm] UpdateProfileRequest request) {
            try {
                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                if (string.IsNullOrEmpty(currentUserId)) {
                    return Unauthorized(new Response {
                        IsSuccess = false,
                        Message = "Unauthorized: Unable to determine the current user"
                    });
                }

                // Validate request
                if (request == null) {
                    return BadRequest(new Response {
                        IsSuccess = false,
                        Message = "Invalid request data"
                    });
                }

                // Handle file upload if provided
                if (request.ProfileImage != null) {
                    try {
                        var uploadResult = await _accountService.UploadFile(request.ProfileImage, "ProfileImages");
                        request.ProfileImageUrl = uploadResult;
                    }
                    catch (Exception ex) {
                        return StatusCode(500, new Response {
                            IsSuccess = false,
                            Message = "Failed to upload profile image: " + ex.Message
                        });
                    }
                }

                var response = await _accountService.UpdateProfileAsync(currentUserId, request);
                return Ok(response);
            }
            catch (Exception ex) {
                return StatusCode(500, new Response {
                    IsSuccess = false,
                    Message = "An error occurred while updating profile: " + ex.Message
                });
            }
        }
    }





}

