import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, Observable, takeUntil } from 'rxjs';

import {
  UserProfileService,
  UserProfile,
} from '../../../Core/Services/UserProfile.service';
import { ThemeService, ThemeMode } from '../../chattrix/Services/Theme.service';
import { ComponentBaseUtil } from '../../../Core/Utils/component-base.util';
import { AuthenticationService } from '../../authentication/Services/Authentication.service';
import { ChangePasswordRequest } from '../../authentication/Models';

@Component({
  selector: 'app-change-password',
  standalone: false,
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss',
})
export class ChangePasswordComponent implements OnInit {
  // User profile data
  userProfile$: Observable<UserProfile | null>;
  currentTheme$: Observable<ThemeMode>;

  // Form and UI state
  passwordForm: FormGroup;
  isSubmitting = false;

  // Password visibility toggles
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  // Tab navigation
  selectedTabIndex = 1; // Change password tab

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userProfileService: UserProfileService,
    private themeService: ThemeService,
    private componentBaseUtil: ComponentBaseUtil,
    private authService: AuthenticationService,
  ) {
    this.userProfile$ = this.userProfileService.userProfile$;
    this.currentTheme$ = this.themeService.currentTheme$;

    // Initialize form
    this.passwordForm = this.createPasswordForm();
  }

  ngOnInit(): void {}

  private createPasswordForm(): FormGroup {
    return this.fb.group(
      {
        currentPassword: ['', [Validators.required]],
        newPassword: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            this.passwordStrengthValidator,
          ],
        ],
        confirmPassword: ['', [Validators.required]],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  /**
   * Custom validator for password strength
   */
  private passwordStrengthValidator(
    control: AbstractControl,
  ): { [key: string]: any } | null {
    const value = control.value;
    if (!value) return null;

    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumeric = /[0-9]/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    const isValidLength = value.length >= 8;

    const passwordValid =
      hasUpperCase &&
      hasLowerCase &&
      hasNumeric &&
      hasSpecialChar &&
      isValidLength;

    if (!passwordValid) {
      return {
        passwordStrength: {
          hasUpperCase,
          hasLowerCase,
          hasNumeric,
          hasSpecialChar,
          isValidLength,
        },
      };
    }

    return null;
  }

  /**
   * Custom validator to check if passwords match
   */
  private passwordMatchValidator(
    group: AbstractControl,
  ): { [key: string]: any } | null {
    const newPassword = group.get('newPassword');
    const confirmPassword = group.get('confirmPassword');

    if (!newPassword || !confirmPassword) return null;

    if (newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      // Clear the error if passwords match
      if (confirmPassword.errors?.['passwordMismatch']) {
        delete confirmPassword.errors['passwordMismatch'];
        if (Object.keys(confirmPassword.errors).length === 0) {
          confirmPassword.setErrors(null);
        }
      }
    }

    return null;
  }

  /**
   * Gets the display name for the user
   */
  getUserDisplayName(profile: UserProfile | null): string {
    if (!profile) return 'User';
    return profile.displayName || profile.name || profile.email || 'User';
  }

  /**
   * Gets the user initials for avatar display
   */
  getUserInitials(profile: UserProfile | null): string {
    if (!profile) return 'U';
    return profile.initials || 'U';
  }

  /**
   * Gets the role display string
   */
  getRoleDisplay(profile: UserProfile | null): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.role)) {
      return profile.role.join(', ');
    }
    return profile.role || 'User';
  }

  /**
   * Handles tab change navigation
   */
  onTabChange(index: number): void {
    this.selectedTabIndex = index;

    if (index === 0) {
      // Navigate to profile component
      this.router.navigate(['/settings/profile']);
    }
  }

  /**
   * Toggles password visibility
   */
  togglePasswordVisibility(field: 'current' | 'new' | 'confirm'): void {
    switch (field) {
      case 'current':
        this.showCurrentPassword = !this.showCurrentPassword;
        break;
      case 'new':
        this.showNewPassword = !this.showNewPassword;
        break;
      case 'confirm':
        this.showConfirmPassword = !this.showConfirmPassword;
        break;
    }
  }

  /**
   * Handles password change form submission
   */
  onSubmitPassword(): void {
    if (this.passwordForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      // Get current user info for userId
      const userInfo = this.authService.getUserInfo();
      if (!userInfo || !userInfo.id) {
        this.componentBaseUtil.showError('User not authenticated');
        this.isSubmitting = false;
        return;
      }

      const changePasswordData: ChangePasswordRequest = {
        userId: userInfo.id,
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword,
        confirmPassword: this.passwordForm.value.confirmPassword,
      };

      this.authService.changePassword(changePasswordData).subscribe({
        next: (response) => {
          if (this.isSubmitting) {
            if (response.isSuccess) {
              this.componentBaseUtil.showSuccess(
                'Password changed successfully!',
              );
              this.passwordForm.reset();

              // this.router.navigate(['/settings/profile']);
            } else {
              this.componentBaseUtil.showError(
                response.message || 'Failed to change password',
              );
            }
            this.isSubmitting = false;
          } else {
            console.warn(
              'Change password: Component no longer active, ignoring response',
            );
          }
        },
        error: (error) => {
          if (
            this.componentBaseUtil.isComponentActive(
              this.isSubmitting,
              'Change password',
            )
          ) {
            const errorMessage = this.componentBaseUtil.extractErrorMessage(
              error,
              'An error occurred while changing password',
            );
            this.componentBaseUtil.showError(errorMessage);
            this.isSubmitting = false;
          }
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Handles form cancellation
   */
  onCancelPassword(): void {
    this.passwordForm.reset();
    this.passwordForm.markAsUntouched();
    this.router.navigate(['/settings/profile']);
  }

  /**
   * Marks all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.passwordForm.controls).forEach((key) => {
      const control = this.passwordForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Gets form field error message
   */
  getFieldError(fieldName: string): string {
    const control = this.passwordForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (control.errors['minlength']) {
        return `Password must be at least ${control.errors['minlength'].requiredLength} characters`;
      }
      if (control.errors['passwordStrength']) {
        return 'Password must contain uppercase, lowercase, number, and special character';
      }
      if (control.errors['passwordMismatch']) {
        return 'Passwords do not match';
      }
    }
    return '';
  }

  /**
   * Gets password strength requirements
   */
  getPasswordRequirements(): string[] {
    const control = this.passwordForm.get('newPassword');
    const requirements = [];

    if (control?.errors?.['passwordStrength']) {
      const strength = control.errors['passwordStrength'];
      if (!strength.isValidLength) requirements.push('At least 8 characters');
      if (!strength.hasUpperCase) requirements.push('One uppercase letter');
      if (!strength.hasLowerCase) requirements.push('One lowercase letter');
      if (!strength.hasNumeric) requirements.push('One number');
      if (!strength.hasSpecialChar) requirements.push('One special character');
    }

    return requirements;
  }
}
