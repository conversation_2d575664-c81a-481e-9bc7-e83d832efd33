{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-form.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, NgModule } from '@angular/core';\nimport { ReplaySubject, BehaviorSubject } from 'rxjs';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzFormItemFeedbackIconComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.iconType);\n  }\n}\nclass NzFormStatusService {\n  formStatusChanges = new ReplaySubject(1);\n  static ɵfac = function NzFormStatusService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormStatusService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzFormStatusService,\n    factory: NzFormStatusService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// Used in input-group/input-number-group to make sure components in addon work well\nclass NzFormNoStatusService {\n  noFormStatus = new BehaviorSubject(false);\n  static ɵfac = function NzFormNoStatusService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormNoStatusService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzFormNoStatusService,\n    factory: NzFormNoStatusService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormNoStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst iconTypeMap = {\n  error: 'close-circle-fill',\n  validating: 'loading',\n  success: 'check-circle-fill',\n  warning: 'exclamation-circle-fill'\n};\nclass NzFormItemFeedbackIconComponent {\n  cdr;\n  status = '';\n  constructor(cdr) {\n    this.cdr = cdr;\n  }\n  iconType = null;\n  ngOnChanges(_changes) {\n    this.updateIcon();\n  }\n  updateIcon() {\n    this.iconType = this.status ? iconTypeMap[this.status] : null;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function NzFormItemFeedbackIconComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormItemFeedbackIconComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormItemFeedbackIconComponent,\n    selectors: [[\"nz-form-item-feedback-icon\"]],\n    hostAttrs: [1, \"ant-form-item-feedback-icon\"],\n    hostVars: 8,\n    hostBindings: function NzFormItemFeedbackIconComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-form-item-feedback-icon-error\", ctx.status === \"error\")(\"ant-form-item-feedback-icon-warning\", ctx.status === \"warning\")(\"ant-form-item-feedback-icon-success\", ctx.status === \"success\")(\"ant-form-item-feedback-icon-validating\", ctx.status === \"validating\");\n      }\n    },\n    inputs: {\n      status: \"status\"\n    },\n    exportAs: [\"nzFormFeedbackIcon\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"nzType\"]],\n    template: function NzFormItemFeedbackIconComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzFormItemFeedbackIconComponent_Conditional_0_Template, 1, 1, \"nz-icon\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.iconType ? 0 : -1);\n      }\n    },\n    dependencies: [NzIconModule, i1.NzIconDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemFeedbackIconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item-feedback-icon',\n      exportAs: 'nzFormFeedbackIcon',\n      imports: [NzIconModule],\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (iconType) {\n      <nz-icon [nzType]=\"iconType\" />\n    }\n  `,\n      host: {\n        class: 'ant-form-item-feedback-icon',\n        '[class.ant-form-item-feedback-icon-error]': 'status===\"error\"',\n        '[class.ant-form-item-feedback-icon-warning]': 'status===\"warning\"',\n        '[class.ant-form-item-feedback-icon-success]': 'status===\"success\"',\n        '[class.ant-form-item-feedback-icon-validating]': 'status===\"validating\"'\n      }\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    status: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated Will be removed in v20. Use `NzFormItemFeedbackIconComponent` directly\n */\nclass NzFormPatchModule {\n  static ɵfac = function NzFormPatchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormPatchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzFormPatchModule,\n    imports: [NzFormItemFeedbackIconComponent],\n    exports: [NzFormItemFeedbackIconComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzFormItemFeedbackIconComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormPatchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFormItemFeedbackIconComponent],\n      exports: [NzFormItemFeedbackIconComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormPatchModule, NzFormStatusService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzC;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,oBAAoB,IAAI,cAAc,CAAC;AAAA,EACvC,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,eAAe,IAAI,gBAAgB,KAAK;AAAA,EACxC,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC;AAAA,EACA,SAAS;AAAA,EACT,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,SAAK,WAAW,KAAK,SAAS,YAAY,KAAK,MAAM,IAAI;AACzD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wCAAwC,mBAAmB;AAChF,WAAO,KAAK,qBAAqB,kCAAoC,kBAAqB,iBAAiB,CAAC;AAAA,EAC9G;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,IAC1C,WAAW,CAAC,GAAG,6BAA6B;AAAA,IAC5C,UAAU;AAAA,IACV,cAAc,SAAS,6CAA6C,IAAI,KAAK;AAC3E,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qCAAqC,IAAI,WAAW,OAAO,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,0CAA0C,IAAI,WAAW,YAAY;AAAA,MACrR;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;AAAA,IACtB,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,WAAW,CAAC;AAAA,MAC7F;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AAAA,MACxC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,eAAe;AAAA,IAC/C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,YAAY;AAAA,MACtB,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6CAA6C;AAAA,QAC7C,+CAA+C;AAAA,QAC/C,+CAA+C;AAAA,QAC/C,kDAAkD;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,+BAA+B;AAAA,IACzC,SAAS,CAAC,+BAA+B;AAAA,EAC3C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,+BAA+B;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,+BAA+B;AAAA,MACzC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}